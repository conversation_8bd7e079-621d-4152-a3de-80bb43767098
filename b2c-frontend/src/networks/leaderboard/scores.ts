/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  LeaderBoardFetchParamsI,
  LeaderBoardAPIResponseI,
} from '@/src/screens/LeaderBoard/components/LeaderBoardScore/types';
import { apiCall } from '@/src/services/api';

export const fetchLeaderboardAPI = async (
  params: LeaderBoardFetchParamsI,
): Promise<LeaderBoardAPIResponseI[]> => {
  const result = await apiCall<unknown, LeaderBoardAPIResponseI[]>(
    '/backend/api/v1/leaderboard',
    'GET',
    {
      isAuth: true,
      query: {
        duration: params.duration,
        type: params.type,
      },
    },
  );

  return result;
};
