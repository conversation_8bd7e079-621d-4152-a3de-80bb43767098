/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ViewStyle } from 'react-native';

export interface VideoPlayerProps {
  source: string;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  resizeMode?: 'contain' | 'cover' | 'stretch';
  onPress?: () => void;
  style?: ViewStyle;
  borderRadius?: number;
  controlButtonSize?: number;
}
