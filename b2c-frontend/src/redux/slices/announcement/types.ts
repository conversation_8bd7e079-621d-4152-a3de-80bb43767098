export type AnnouncementState = {
  data: {
    center: number[];
    context: MapboxContext[];
    id: string;
    geometry: {
      type: string;
      coordinates: number[];
    };
    place_name: string;
    place_type: string[];
    properties: {
      accuracy: string;
      mapbox_id: string;
    };
    relevance: number;
    text: string;
    type: string;
  };
  filters: {
    selfLocation: {
      coords: {
        latitude: number;
        longitude: number;
      };
      name: string;
    };
    otherLocation: {
      coords: {
        latitude: number;
        longitude: number;
      };
      name: string;
    };
    radius: number;
  };
};

export type MapboxContext = {
  id?: string;
  mapbox_id: string;
  wikidata: string;
  short_code: string;
  text: string;
};
