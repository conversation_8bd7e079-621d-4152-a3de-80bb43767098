import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import ScoreboardItem from './components/ScoreboardItem';
import { fetchScoreboardAPI } from '@/src/networks/scoreboard/scoreboard';
import type { ScoreboardDataI } from './types';

const ScoreboardScreen = () => {
    const navigation = useNavigation<BottomTabNavigationI>();
    const [scoreboardData, setScoreboardData] = useState<ScoreboardDataI[]>([]);
    const [loading, setLoading] = useState(true);

    const handleBack = () => {
        navigation.goBack();
    };



    const fetchScoreboardData = async () => {
        try {
            setLoading(true);
            const apiData = await fetchScoreboardAPI();
            console.log(apiData, 'apiData')
            if (apiData) {

                const transformedData: ScoreboardDataI[] = [
                    {
                        category: 'Troubleshooting',
                        score: apiData.troubleshootAnswerScore,
                        index: apiData.troubleshootPerformanceIndex
                    },
                    {
                        category: 'Qna',
                        score: apiData.qnaAnswerScore,
                        index: apiData.qnaPerformanceIndex
                    },
                    {
                        category: 'Contributor',
                        score: apiData.contributionScore,
                        index: apiData.contributionPerformanceIndex
                    },
                ];
                setScoreboardData(transformedData);
            } else {
                setScoreboardData([]);
            }
        } catch (error) {
            console.error('Failed to fetch scoreboard data:', error);
            setScoreboardData([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchScoreboardData();
    }, []);

    return (
        <SafeArea>
            <View className="flex-1 bg-white">
                <View className="px-4 pt-2 pb-4">
                    <BackButton
                        onBack={handleBack}
                        label="My scoreboard"
                        labelClassname="text-xl font-semibold text-gray-900"
                    />
                </View>

                <View className="px-4">
                    <View className="flex-row items-center mb-4 px-4 bg-[#F6F4F4] p-4 py-7">
                        <View className="flex-1">
                            <Text className="text-sm font-medium text-[#7E7D7D]">Category</Text>
                        </View>
                        <View className="w-32 items-center">
                            <Text className="text-sm font-medium text-[#7E7D7D]">Score</Text>
                        </View>
                        <View className="w-32 items-end">
                            <Text className="text-sm text-end font-medium text-[#7E7D7D]">Leaderboard Index</Text>
                        </View>
                    </View>

                    {/* Scoreboard Items */}
                    {loading ? (
                        <View className="flex-1 justify-center items-center py-8">
                            <Text className="text-gray-500">Loading scoreboard...</Text>
                        </View>
                    ) : (
                        scoreboardData.map((item, index) => (
                            <ScoreboardItem
                                key={index}
                                category={item.category}
                                score={item.score}
                                index={item.index}
                            />
                        ))
                    )}
                </View>
            </View>
        </SafeArea>
    );
};

export default ScoreboardScreen;