import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI } from '@interfaces/reward/scoreboard';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

type UserRewardProfileI = {
  contributionScore: number;
  qnaAnswerScore: number;
  troubleshootAnswerScore: number;
  totalScore: number;
};

type MaxScoresI = {
  maxContributionScore: number | null;
  maxQnaAnswerScore: number | null;
  maxTroubleshootAnswerScore: number | null;
  maxTotalScore: number | null;
};

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI): Promise<ScoreboardFetchForClientResultI[]> => {
    const profileId = request.profileId;

    if (!profileId) {
      throw new AppError('AUTH019');
    }

    // Get user's reward profile using raw query
    const userRewardProfileResult = await prismaPG.$queryRaw<UserRewardProfileI[]>(
      Prisma.sql`
        SELECT
          "contributionScore",
          "qnaAnswerScore",
          "troubleshootAnswerScore",
          "totalScore"
        FROM "score"."RewardProfile"
        WHERE "profileId" = ${profileId}
      `
    );

    // If user has no reward profile, return empty scores
    if (!userRewardProfileResult || userRewardProfileResult.length === 0) {
      return [
        {
          category: 'Mentor',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Guide',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Troubleshooter',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Contributor',
          score: 0,
          leaderboardIndex: 0,
        },
      ];
    }

    const userRewardProfile = userRewardProfileResult[0];

    // Get highest scores for each category using raw query
    const maxScoresResult = await prismaPG.$queryRaw<MaxScoresI[]>(
      Prisma.sql`
        SELECT
          MAX("contributionScore") as "maxContributionScore",
          MAX("qnaAnswerScore") as "maxQnaAnswerScore",
          MAX("troubleshootAnswerScore") as "maxTroubleshootAnswerScore",
          MAX("totalScore") as "maxTotalScore"
        FROM "score"."RewardProfile"
      `
    );

    const maxScores = maxScoresResult[0];

    // Calculate leaderboard index: (user score / highest score) * 100
    const calculateLeaderboardIndex = (userScore: number, maxScore: number | null): number => {
      if (!maxScore || maxScore === 0) return 0;
      return Math.round((userScore / maxScore) * 100);
    };

    // Map the scores to the frontend categories
    const scoreboardData: ScoreboardFetchForClientResultI[] = [
      {
        category: 'Mentor',
        score: userRewardProfile.totalScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.totalScore,
          maxScores.maxTotalScore
        ),
      },
      {
        category: 'Guide',
        score: userRewardProfile.qnaAnswerScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.qnaAnswerScore,
          maxScores.maxQnaAnswerScore
        ),
      },
      {
        category: 'Troubleshooter',
        score: userRewardProfile.troubleshootAnswerScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.troubleshootAnswerScore,
          maxScores.maxTroubleshootAnswerScore
        ),
      },
      {
        category: 'Contributor',
        score: userRewardProfile.contributionScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.contributionScore,
          maxScores.maxContributionScore
        ),
      },
    ];

    return scoreboardData;
  },
};
