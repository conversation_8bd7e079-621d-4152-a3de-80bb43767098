import type { ListItem } from '@/src/components/UsersList/types';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type NearbyListItem = ListItem & {
  latitude: string;
  longitude: string;
  distanceInMetres: number;
};

export type ProfileMarkerI = {
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designation: SearchResultI | null;
    entity: SearchResultI | null;
  };
  longitude: number;
  latitude: number;
};
