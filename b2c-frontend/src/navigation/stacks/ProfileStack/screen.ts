import BlockedProfiles from '@/src/screens/BlockedProfiles';
import ChatScreen from '@/src/screens/Chat';
import CommentScreen from '@/src/screens/Comment';
import ConnectionScreen from '@/src/screens/Connection';
import EditCargoItemScreen from '@/src/screens/EditCargoItem';
import EditCertificationItemScreen from '@/src/screens/EditCertificationItem';
import EditCertificationListScreen from '@/src/screens/EditCertificationList';
import EditDetailScreen from '@/src/screens/EditDetail';
import EditDocumentItemScreen from '@/src/screens/EditDocumentItem';
import EditDocumentsListScreen from '@/src/screens/EditDocumentsList';
import EditEducationItemScreen from '@/src/screens/EditEducationItem';
import EditEducationListScreen from '@/src/screens/EditEducationList';
import EditEquipmentItemScreen from '@/src/screens/EditEquipmentItem';
import EditExperienceItemScreen from '@/src/screens/EditExperienceItem';
import EditExperienceListScreen from '@/src/screens/EditExperienceList';
import EditShipItemScreen from '@/src/screens/EditShipItem';
import EditSkillsListScreen from '@/src/screens/EditSkillsList';
import EditUserProfileScreen from '@/src/screens/EditUserProfile';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import LikesScreen from '@/src/screens/Likes';
import PortsVisitedScreen from '@/src/screens/PortsVisited';
import PrivacyPolicyScreen from '@/src/screens/PrivacyPolicy';
import ShipProfileScreen from '@/src/screens/ShipProfile';
import TermsOfServiceScreen from '@/src/screens/TermsAndCondition';
import UserProfileScreen from '@/src/screens/UserProfile';
import UserSettingScreen from '@/src/screens/UserSettings';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import { ProfileStackParamsListI, StackScreenI } from '../../types';
import ScoreboardScreen from '@/src/screens/Scoreboard';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'UserProfile',
    component: UserProfileScreen,
    title: 'Profile Error',
    subtitle: 'Something went wrong loading the profile. Please try again.',
  },
  {
    name: 'OtherUserProfile',
    component: UserProfileScreen,
    title: 'Profile Error',
    subtitle: 'Something went wrong loading the profile. Please try again.',
  },
  {
    name: 'Connection',
    component: ConnectionScreen,
    title: 'Connections Error',
    subtitle: 'Something went wrong loading connections. Please try again.',
  },
  {
    name: 'Likes',
    component: LikesScreen,
    title: 'Likes Error',
    subtitle: 'Something went wrong loading likes. Please try again.',
  },
  {
    name: 'Comment',
    component: CommentScreen,
    title: 'Comments Error',
    subtitle: 'Something went wrong loading comments. Please try again.',
  },
  {
    name: 'UserSettings',
    component: UserSettingScreen,
    title: 'Settings Error',
    subtitle: 'Something went wrong loading settings. Please try again.',
  },
  {
    name: 'BlockedUserProfiles',
    component: BlockedProfiles,
    title: 'Blocked Profiles Error',
    subtitle: 'Something went wrong loading blocked profiles. Please try again.',
  },
  {
    name: 'EditUserProfile',
    component: EditUserProfileScreen,
    title: 'Edit Profile Error',
    subtitle: 'Something went wrong while editing your profile. Please try again.',
  },
  {
    name: 'SearchScreen',
    component: EntitySearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
  {
    name: 'EditDetail',
    component: EditDetailScreen,
    title: 'Edit Details Error',
    subtitle: 'Something went wrong while editing details. Please try again.',
  },
  {
    name: 'EditDocumentList',
    component: EditDocumentsListScreen,
    title: 'Documents Error',
    subtitle: 'Something went wrong loading your documents. Please try again.',
  },
  {
    name: 'EditDocumentItem',
    component: EditDocumentItemScreen,
    title: 'Document Edit Error',
    subtitle: 'Something went wrong while editing the document. Please try again.',
  },
  {
    name: 'EditCertificationList',
    component: EditCertificationListScreen,
    title: 'Certifications Error',
    subtitle: 'Something went wrong loading your certifications. Please try again.',
  },
  {
    name: 'EditCertificationItem',
    component: EditCertificationItemScreen,
    title: 'Certification Edit Error',
    subtitle: 'Something went wrong while editing the certification. Please try again.',
  },
  {
    name: 'EditExperienceList',
    component: EditExperienceListScreen,
    title: 'Experience Error',
    subtitle: 'Something went wrong loading your experience. Please try again.',
  },
  {
    name: 'EditExperienceItem',
    component: EditExperienceItemScreen,
    title: 'Experience Edit Error',
    subtitle: 'Something went wrong while editing the experience. Please try again.',
  },
  {
    name: 'EditEducationList',
    component: EditEducationListScreen,
    title: 'Education Error',
    subtitle: 'Something went wrong loading your education. Please try again.',
  },
  {
    name: 'EditEducationItem',
    component: EditEducationItemScreen,
    title: 'Education Edit Error',
    subtitle: 'Something went wrong while editing the education. Please try again.',
  },
  {
    name: 'EditSkillsList',
    component: EditSkillsListScreen,
    title: 'Skills Error',
    subtitle: 'Something went wrong loading your skills. Please try again.',
  },
  {
    name: 'PortsVisited',
    component: PortsVisitedScreen,
    title: 'Ports Visited Error',
    subtitle: 'Something went wrong loading ports visited. Please try again.',
  },
  {
    name: 'EditShipItem',
    component: EditShipItemScreen,
    title: 'Ship Edit Error',
    subtitle: 'Something went wrong while editing the ship details. Please try again.',
  },
  {
    name: 'EditEquipmentItem',
    component: EditEquipmentItemScreen,
    title: 'Equipment Edit Error',
    subtitle: 'Something went wrong while editing the equipment. Please try again.',
  },
  {
    name: 'EditCargoItem',
    component: EditCargoItemScreen,
    title: 'Cargo Edit Error',
    subtitle: 'Something went wrong while editing the cargo details. Please try again.',
  },
  {
    name: 'ShipProfile',
    component: ShipProfileScreen,
    title: 'Ship Profile Error',
    subtitle: 'Something went wrong loading the ship profile. Please try again.',
  },
  {
    name: 'Privacy',
    component: PrivacyPolicyScreen,
    title: 'Privacy Policy Error',
    subtitle: 'Something went wrong loading the privacy policy. Please try again.',
  },
  {
    name: 'Terms',
    component: TermsOfServiceScreen,
    title: 'Terms of Service Error',
    subtitle: 'Something went wrong loading the terms of service. Please try again.',
  },
  {
    name: 'Chat',
    component: ChatScreen,
    title: 'Chat Error',
    subtitle: 'Something went wrong in the chat. Please try again.',
  },
  {
    name: 'Scoreboard',
    component: ScoreboardScreen,
    title: 'Scoreboard Error',
    subtitle: 'Something went wrong loading the scoreboard. Please try again.',
  },
];

export const screens: StackScreenI<ProfileStackParamsListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof ProfileStackParamsListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
