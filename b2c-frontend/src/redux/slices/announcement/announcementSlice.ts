import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { AnnouncementState } from './types';

const initialState: AnnouncementState = {
  data: {
    center: [],
    context: [],
    id: '',
    geometry: {
      type: '',
      coordinates: [],
    },
    place_name: '',
    place_type: [],
    properties: {
      accuracy: '',
      mapbox_id: '',
    },
    relevance: 0,
    text: '',
    type: '',
  },
  filters: {
    selfLocation: {
      coords: {
        latitude: 0,
        longitude: 0,
      },
      name: '',
    },
    otherLocation: {
      coords: {
        latitude: 0,
        longitude: 0,
      },
      name: '',
    },
    radius: 40,
  },
};

const announcementSlice = createSlice({
  name: 'announcement',
  initialState,
  reducers: {
    setLocationData: (state, action: PayloadAction<any>) => {
      state.data = action.payload;
    },
    clearLocationData: (state) => {
      state.data = {
        center: [],
        context: [],
        id: '',
        geometry: {
          type: '',
          coordinates: [],
        },
        place_name: '',
        place_type: [],
        properties: {
          accuracy: '',
          mapbox_id: '',
        },
        relevance: 0,
        text: '',
        type: '',
      };
    },
    setOtherLocation: (state, action: PayloadAction<any>) => {
      state.filters.otherLocation = {
        coords: {
          latitude: action.payload.coords.latitude,
          longitude: action.payload.coords.longitude,
        },
        name: action.payload.name,
      };
    },
    clearOtherLocation: (state) => {
      state.filters.otherLocation = {
        coords: {
          latitude: 0,
          longitude: 0,
        },
        name: '',
      };
    },
    setSelfLocation: (state, action: PayloadAction<any>) => {
      state.filters.selfLocation = {
        coords: {
          latitude: action.payload.coords.latitude,
          longitude: action.payload.coords.longitude,
        },
        name: action.payload.name,
      };
    },
    clearSelfLocation: (state) => {
      state.filters.selfLocation = {
        coords: {
          latitude: 0,
          longitude: 0,
        },
        name: '',
      };
    },
    setNearbyRadius: (state, action: PayloadAction<any>) => {
      state.filters.radius = action.payload;
    },
  },
});

export const {
  setLocationData,
  clearLocationData,
  setOtherLocation,
  setSelfLocation,
  clearOtherLocation,
  clearSelfLocation,
  setNearbyRadius,
} = announcementSlice.actions;

export default announcementSlice.reducer;
