import { View, Text, Image, StyleSheet } from 'react-native';
import MaskedAvatar from '@/src/assets/svgs/MaskedAvatar';
import type { MapAvatarProps } from './types';

const MapAvatar = ({
  uri,
  name,
  profileId,
  onImageLoaded,
  isAnonymous,
  isSelfUser,
}: MapAvatarProps) => {
  if (!isSelfUser) {
    if (isAnonymous) {
      return <MaskedAvatar width={4.5} height={4.5} />;
    }
  }

  const initial = name ? name.charAt(0).toUpperCase() : '';
  const showOverlay = isSelfUser && isAnonymous;

  const handleLoadEnd = () => {
    if (profileId && onImageLoaded) {
      onImageLoaded(profileId);
    }
  };

  const handleError = () => {
    if (profileId && onImageLoaded) {
      onImageLoaded(profileId);
    }
  };

  const hasValidImageUri =
    (typeof uri === 'string' && uri !== '') ||
    (typeof uri === 'object' && uri !== null && 'uri' in uri);

  const imageSource = typeof uri === 'string' ? { uri } : uri;

  return (
    <View style={styles.container} collapsable={false}>
      <View style={styles.avatarContainer}>
        {hasValidImageUri ? (
          <Image
            source={{ uri: imageSource?.uri }}
            style={styles.image}
            onLoadEnd={handleLoadEnd}
            onError={handleError}
          />
        ) : (
          <View style={[styles.image, styles.initialsContainer]}>
            <Text style={styles.initialsText}>{initial}</Text>
          </View>
        )}
        {showOverlay && <View style={styles.overlay} />}
      </View>
      {name && <Text style={styles.name}>{name}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    width: 60,
  },
  avatarContainer: {
    position: 'relative',
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'white',
  },
  initialsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#006400',
  },
  initialsText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  overlay: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(128, 128, 128, 0.5)',
  },
  name: {
    marginTop: 4,
    fontSize: 10,
    fontWeight: 'bold',
    backgroundColor: 'white',
    paddingHorizontal: 4,
    borderRadius: 4,
    zIndex: 1,
  },
});

export default MapAvatar;
