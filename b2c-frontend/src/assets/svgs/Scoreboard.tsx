import { RFPercentage } from "react-native-responsive-fontsize"
import Svg, { Path } from "react-native-svg"

const Scoreboard = ({ width = 2, height = 2 })  => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 20"
      fill="none"
    >
      <Path
        d="M21.648 3.06h-1.082V1.327a.487.487 0 10-.975 0V3.06H4.41V1.33a.488.488 0 10-.975 0v1.73H2.352A2.355 2.355 0 000 5.411v11.396a2.355 2.355 0 002.352 2.352h19.296A2.354 2.354 0 0024 16.807V5.41a2.355 2.355 0 00-2.352-2.352zm1.377 2.351V7.33H12.488V4.034h9.16c.76 0 1.377.618 1.377 1.377zM2.352 4.034h9.16V7.33H.976V5.41c0-.759.618-1.377 1.377-1.377zM.975 16.807V8.305h10.538v9.879H2.352c-.76 0-1.377-.618-1.377-1.377zm20.673 1.377h-9.16v-9.88h10.537v8.503c0 .759-.618 1.377-1.377 1.377z"
        fill="#000"
      />
      <Path
        d="M6.304 9.73h-.118a2.118 2.118 0 00-2.116 2.116v2.779c0 1.166.95 2.115 2.116 2.115h.118a2.118 2.118 0 002.116-2.115v-2.78A2.118 2.118 0 006.304 9.73zm1.14 4.895c0 .629-.511 1.14-1.14 1.14h-.118c-.63 0-1.14-.511-1.14-1.14v-2.78c0-.628.51-1.14 1.14-1.14h.118c.629 0 1.14.512 1.14 1.14v2.78zM17.82 9.73H17.7a2.118 2.118 0 00-2.115 2.116v2.779c0 1.166.949 2.115 2.115 2.115h.119a2.118 2.118 0 002.115-2.115v-2.78A2.118 2.118 0 0017.82 9.73zm1.14 4.895c0 .629-.511 1.14-1.14 1.14H17.7c-.628 0-1.14-.511-1.14-1.14v-2.78c0-.628.512-1.14 1.14-1.14h.119c.629 0 1.14.512 1.14 1.14v2.78z"
        fill="#000"
      />
    </Svg>
  )
}

export default Scoreboard

