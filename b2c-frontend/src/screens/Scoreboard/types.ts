/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type CircularProgressProps = {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
};

export type ScoreboardItemProps = {
  category: string;
  score: number;
  index: number;
};

export type ScoreboardDataI = {
  category: string;
  score: number;
  index: number;
};

// API Types - Raw RewardProfile data
export type RewardProfileI = {
  profileId: string;
  contributionScore: number;
  qnaAnswerScore: number;
  troubleshootAnswerScore: number;
  totalScore: number;
  createdAt: string;
  updatedAt: string;
};

export type ScoreboardAPIResponseI = RewardProfileI;
