/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Image, Pressable, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { RFPercentage } from 'react-native-responsive-fontsize';
import TrashBin from '@/src/assets/svgs/TrashBin';
import PlayIcon from '@/src/assets/svgs/Play';
import VideoPlayer from '@/src/components/VideoPlayer';
import { PostThumbnailProps } from './types';

const PostThumbnail = ({ uri, type, isSelected, onPress, handleTrash }: PostThumbnailProps) => {
  const size = RFPercentage(5);
  const trashSize = RFPercentage(0.2);
  const isVideo = type?.startsWith('video/') || false;

  return (
    <Pressable onPress={() => onPress?.(uri)} className="relative rounded-md overflow-hidden">
      {isVideo ? (
        <View style={{ width: size, height: size, borderRadius: 6, overflow: 'hidden' }}>
          <VideoPlayer
            source={uri}
            width={size}
            height={size}
            borderRadius={6}
            showControls={false}
            muted={true}
            controlButtonSize={6}
          />
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
            }}
          >
            <PlayIcon width={4} height={4} fill="white" />
          </View>
        </View>
      ) : (
        <Image className="rounded-md" source={{ uri }} width={size} height={size} alt="Thumbnail" />
      )}
      {isSelected && (
        <View className="absolute w-full h-full justify-end items-center" style={{ height: size }}>
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.6)']}
            style={{ height: size, width: '100%' }}
          />
          <Pressable
            onPress={handleTrash}
            className="absolute top-0 bottom-0 w-full h-full justify-center items-center"
            style={{ marginBottom: 4 }}
          >
            <TrashBin color="white" width={trashSize} height={trashSize} />
          </Pressable>
        </View>
      )}
    </Pressable>
  );
};

export default PostThumbnail;
