import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import ScoreboardItem from './components/ScoreboardItem';

const ScoreboardScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleBack = () => {
    navigation.goBack();
  };

  // Mock data - replace with actual data from API
  const scoreboardData = [
    { category: 'Mentor', score: 24, percentage: 80 },
    { category: 'Guide', score: 70, percentage: 80 },
    { category: 'Troubleshooter', score: 24, percentage: 80 },
    { category: 'Contributor', score: 32, percentage: 80 },
  ];

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 pt-2 pb-4">
          <BackButton
            onBack={handleBack}
            label="My scoreboard"
            labelClassname="text-xl font-semibold text-gray-900"
          />
        </View>

        <View className="px-4">
          {/* Header Row */}
          <View className="flex-row items-center justify-between mb-4 px-4">
            <Text className="text-base font-medium text-gray-500 flex-1">Category</Text>
            <Text className="text-base font-medium text-gray-500 w-12 text-center">Score</Text>
            <Text className="text-base font-medium text-gray-500 w-16 text-center">%</Text>
          </View>

          {/* Scoreboard Items */}
          {scoreboardData.map((item, index) => (
            <ScoreboardItem
              key={index}
              category={item.category}
              score={item.score}
              percentage={item.percentage}
            />
          ))}
        </View>
      </View>
    </SafeArea>
  );
};

export default ScoreboardScreen;