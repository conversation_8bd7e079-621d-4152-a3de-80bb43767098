/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, View } from 'react-native';
import CircularProgress from '../CircularProgress';
import type { ScoreboardItemProps } from '../../types';

const ScoreboardItem = ({ category, score, percentage }: ScoreboardItemProps) => {
  return (
    <View className="bg-gray-50 rounded-lg p-4 mb-3">
      <View className="flex-row items-center justify-between">
        <View className="flex-1">
          <Text className="text-lg font-medium text-gray-900 mb-1">{category}</Text>
        </View>
        <View className="flex-row items-center space-x-4">
          <Text className="text-lg font-semibold text-gray-900 w-12 text-center">{score}</Text>
          <CircularProgress percentage={percentage} />
        </View>
      </View>
    </View>
  );
};

export default ScoreboardItem;
