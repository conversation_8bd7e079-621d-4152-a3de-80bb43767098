import { apiCall } from '@/src/services/api';
import {
  CreateReferralCodePayloadI,
  ReferralCodeResponseI,
  CreateReferralStatusPayloadI,
  ReferralStatusResponseI,
  CompleteOnboardingPayloadI,
  Pagination,
  ReferredPeopleResponse,
} from './types';

export const createReferralCodeAPI = async (
  payload: CreateReferralCodePayloadI,
): Promise<ReferralCodeResponseI> => {
  return apiCall('/backend/api/v1/referral/code', 'POST', {
    isAuth: true,
    payload,
  });
};

export const createReferralStatusAPI = async (
  payload: CreateReferralStatusPayloadI,
): Promise<ReferralStatusResponseI> => {
  return apiCall('/backend/api/v1/referral/status', 'POST', {
    isAuth: true,
    payload,
  });
};

export const completeReferralOnboardingAPI = async (
  payload: CompleteOnboardingPayloadI,
): Promise<ReferralStatusResponseI> => {
  return apiCall('/backend/api/v1/referral/onboarding', 'PATCH', {
    isAuth: true,
    payload,
  });
};

export const fetchReferredPeopleAPI = async (
  query: Pagination,
): Promise<ReferredPeopleResponse> => {
  return apiCall('/backend/api/v1/referral/referred', 'GET', {
    isAuth: true,
    query,
  });
};
