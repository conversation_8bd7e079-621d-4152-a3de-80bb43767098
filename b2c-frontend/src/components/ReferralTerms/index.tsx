import {
  View,
  Text,
  ScrollView,
  Modal,
  Pressable,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { Image } from 'react-native';
import Markdown from 'react-native-markdown-display';
import { ReferralTermsModalProps } from './types';
import { useReferralTermsModal } from './useHook';

const ReferralTermsModal = ({ isVisible, onClose }: ReferralTermsModalProps) => {
  const { termsContent, loading } = useReferralTermsModal(isVisible);

  const cleanContent = termsContent
    ?.replace(/\\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\'/g, "'");

  const markdownStyles = StyleSheet.create({
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: '#374151',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
      marginTop: 24,
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 12,
      marginTop: 20,
    },
    heading3: {
      fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
      marginTop: 16,
    },
    paragraph: {
      marginBottom: 12,
      lineHeight: 22,
    },
    list_item: {
      marginBottom: 8,
    },
    bullet_list: {
      marginBottom: 16,
    },
    ordered_list: {
      marginBottom: 16,
    },
    strong: {
      fontWeight: 'bold',
    },
    em: {
      fontStyle: 'italic',
    },
    link: {
      color: '#004687',
      textDecorationLine: 'underline',
    },
  });

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-white">
        <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
          <Text className="text-xl font-bold text-gray-900">Referral Terms & Conditions</Text>
          <Pressable onPress={onClose} className="p-2">
            <Text className="text-lg text-blue-600 font-medium">Done</Text>
          </Pressable>
        </View>
        {loading ? (
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="small" color="#448600" />
          </View>
        ) : (
          <ScrollView className="flex-1 px-4 py-6" showsVerticalScrollIndicator={false}>
            {cleanContent && (
              <Markdown
                style={markdownStyles}
                rules={{
                  image: (node) => {
                    const { src } = node.attributes;
                    const key = src;

                    return (
                      <Image
                        key={key}
                        source={{ uri: src }}
                        style={{ width: '100%', height: 500, resizeMode: 'contain' }}
                      />
                    );
                  },
                }}
              >
                {cleanContent}
              </Markdown>
            )}
          </ScrollView>
        )}
      </View>
    </Modal>
  );
};

export default ReferralTermsModal;
