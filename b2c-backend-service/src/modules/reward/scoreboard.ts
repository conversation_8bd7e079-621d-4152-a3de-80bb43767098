import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI, RewardProfileI } from '@interfaces/reward/scoreboard';
import { Prisma } from '@prisma/postgres';

export const ScoreboardModule = {
  fetch: async (): Promise<ScoreboardFetchForClientResultI> => {
    const result = await prismaPG.$queryRaw<RewardProfileI>(
      Prisma.sql`
        SELECT * FROM "score"."RewardProfile"
        ORDER BY "totalScore" DESC
      `
    );

    return result?.[0];
  },
};
