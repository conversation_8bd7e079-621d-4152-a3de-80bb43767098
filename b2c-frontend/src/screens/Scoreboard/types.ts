/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type CircularProgressProps = {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
};

export type ScoreboardItemProps = {
  category: string;
  score: number;
  percentage: number;
};

export type ScoreboardDataI = {
  category: string;
  score: number;
  percentage: number;
};

// API Types for future implementation
export type ScoreboardAPIResponseI = {
  mentor: {
    score: number;
    percentage: number;
  };
  guide: {
    score: number;
    percentage: number;
  };
  troubleshooter: {
    score: number;
    percentage: number;
  };
  contributor: {
    score: number;
    percentage: number;
  };
};
