/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { PostExternalClientI } from '@/src/networks/content/types';

export interface PostMediaI {
  caption?: string | null;
  fileUrl: string;
  extension: string;
  id?: string;
  postId?: string;
  profileId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface MediaViewerModalProps {
  isVisible: boolean;
  onClose: () => void;
  post: PostExternalClientI;
  initialIndex?: number;
}

// Keep the old interface for backward compatibility
export interface ImageViewerModalProps extends MediaViewerModalProps {}
