/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { setProfileAsync, updateWorkDetail } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { onboardingWorkAPI } from '@/src/networks/onboarding/work';
import { completeReferralOnboardingAPI } from '@/src/networks/referral';
import { UseWorkDetailsFormI, WorkDetailsFormDataI } from './types';

const useWorkDetails = (
  onNext: (data: WorkDetailsFormDataI) => void,
  personalData: {
    fullName: string;
    country: SearchResultI;
    gender: string;
  },
  onBack?: () => void,
): UseWorkDetailsFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUser = useSelector(selectCurrentUser);
  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<WorkDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      entity: undefined,
      designation: undefined,
    },
  });

  const handleBack = () => {
    const formData = methods.getValues();

    if (formData?.entity?.name && formData?.designation?.name) {
      dispatch(
        setProfileAsync({
          fullName: personalData.fullName,
          country: personalData.country,
          gender: personalData.gender,
          organisation: formData.entity,
          designation: formData.designation,
        }),
      );
    }

    if (onBack) onBack();
  };

  const onSubmit = async (data: WorkDetailsFormDataI) => {
    try {
      setIsSubmitting(true);
      const formData: WorkDetailsFormDataI = {
        entity: data.entity,
        designation: data.designation,
      };

      if (!formData.designation?.id || !formData.designation?.dataType) {
        throw new Error('Designation is required');
      }

      const payload = extractPayload(formData);

      await onboardingWorkAPI(payload);
      dispatch(
        setProfileAsync({
          fullName: personalData.fullName,
          country: personalData.country,
          gender: personalData.gender,
          organisation: formData.entity,
          designation: formData.designation,
        }),
      );
      dispatch(updateWorkDetail());
      dispatch(clearSelection('designation'));
      dispatch(clearSelection('entity'));
      if (currentUser?.referredByCode) {
        await completeReferralOnboardingAPI({
          referredProfileId: currentUser.profileId,
        });
      }
      onNext(formData);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    handleBack,
  };
};

type WorkPayloadType = {
  designation: Pick<SearchResultI, 'id' | 'dataType'>;
  entity?: Pick<SearchResultI, 'id' | 'dataType'>;
};

const extractPayload = (formData: {
  designation: SearchResultI;
  entity?: SearchResultI;
}): WorkPayloadType => {
  const payload: WorkPayloadType = {
    designation: {
      id: formData.designation.id,
      dataType: formData.designation.dataType,
    },
  };

  if (formData?.entity?.id && formData?.entity?.dataType) {
    payload.entity = {
      id: formData.entity.id,
      dataType: formData.entity.dataType,
    };
  }

  return payload;
};

export default useWorkDetails;
