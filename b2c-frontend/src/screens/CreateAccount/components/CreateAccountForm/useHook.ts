/*
Copyright (c) 2025-present Navicater Solutions
This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Config from 'react-native-config';
import appleAuth from '@invertase/react-native-apple-authentication';
import {
  GoogleSignin,
  isSuccessResponse,
  type SignInResponse,
} from '@react-native-google-signin/google-signin';
import { jwtDecode } from 'jwt-decode';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { fetchAndSaveUserProfile } from '@/src/redux/slices/user/userSlice';
import { externalSignInAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import AppError from '@/src/errors/networks/AppError';
import type { AuthStackParamListI } from '@/src/navigation/types';
import { useSocket } from '@/src/context/providers/SocketProvider';
import useNotification from '@/src/hooks/notification';
import useStorage from '@/src/hooks/storage';
import { sendOTPForEmailVerificationAPI } from '@/src/networks/auth/email';
import { registerAPI } from '@/src/networks/auth/register';
import type { AuthLoginResultI } from '@/src/networks/auth/types';
import { createAppLogAPI } from '@/src/networks/log/appLog';
import type { AppLogPayloadI } from '@/src/networks/log/types';
import type { CreateAccountFormDataI, UseCreateAccountFormI } from './types';

GoogleSignin.configure({
  scopes: ['openid', 'profile', 'email'],
  webClientId: Config.WEB_CLIENT_ID,
  iosClientId: Config.IOS_CLIENT_ID,
  profileImageSize: 120,
  offlineAccess: true,
});

const useCreateAccount = (): UseCreateAccountFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleSubmitting, setIsGoogleSubmitting] = useState(false);
  const [isAppleSubmitting, setIsAppleSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<AuthStackParamListI>>();
  const { setStorage } = useStorage();
  const dispatch = useDispatch<AppDispatch>();
  const { getDeviceToken } = useNotification();
  const { connectSocket } = useSocket();
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  const methods = useForm<CreateAccountFormDataI>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      acceptedTerms: false,
    },
  });

  const { setError } = methods;

  const navigateBasedOnUserState = async (authResult: AuthLoginResultI) => {
    const {
      isUsernameSaved,
      isPersonalDetailsSaved,
      isWorkDetailsSaved,
      email,
      isEmailVerified,
      profileId,
      isPrivacyPolicyAccepted,
    } = authResult;
    const { hasSkippedReferral } = useStorage();
    const skippedReferral = await hasSkippedReferral();
    switch (true) {
      case !isEmailVerified:
        navigation.navigate('VerifyEmail', { email, profileId });
        break;
      case !isPrivacyPolicyAccepted:
        navigation.navigate('PolicyAcceptance');
        break;
      case !isUsernameSaved && !skippedReferral:
        navigation.navigate('ReferralCode');
        break;
      case !isUsernameSaved:
        navigation.navigate('SetUsername');
        break;
      case !isPersonalDetailsSaved || !isWorkDetailsSaved:
        navigation.navigate('AddUserDetailScreen');
        break;
      default:
        break;
    }
  };

  const onSubmit = async (data: CreateAccountFormDataI) => {
    try {
      setIsSubmitting(true);
      if (data.password !== data.confirmPassword) {
        setError('confirmPassword', {
          message: 'Passwords do not match',
        });
        return;
      }

      const result = await registerAPI({
        type: 'EMAIL_PASSWORD',
        email: data.email.trim(),
        password: data.password,
        confirmPassword: data.confirmPassword,
        isPPAndTNCAccepted: data.acceptedTerms,
      });

      await setStorage('token', result.token);

      try {
        await sendOTPForEmailVerificationAPI({
          profileId: result.profileId,
        });
        navigation.navigate('VerifyEmail', {
          profileId: result.profileId,
          email: data.email.trim(),
        });
      } catch {
        showToast({
          type: 'error',
          message: 'Account Created',
          description:
            'Account created but failed to send verification email. Please try again later.',
        });
      }
    } catch (error: unknown) {
      if (typeof error === 'string') {
        showToast({
          type: 'error',
          message: error,
        });
      } else if (error instanceof Error) {
        if (error instanceof APIResError) {
          switch (error.code) {
            case 'AUTH005':
              setError('email', {
                message: 'Email already exists. Please use a different email address.',
              });
              break;
            case 'AUTH007':
              showToast({
                type: 'error',
                message: 'Invalid Data',
                description: 'Please check your input and try again',
              });
              break;
            case 'AUTH008':
              showToast({
                type: 'error',
                message: 'Registration Method Not Supported',
                description: 'Please use email and password registration',
              });
              break;
            default:
              showToast({
                type: 'error',
                message: 'Registration Failed',
                description: 'Please check your information and try again',
              });
          }
        } else {
          showToast({
            type: 'error',
            message: error.message || 'Registration Failed',
            description: 'Please try again later',
          });
        }
      } else {
        showToast({
          type: 'error',
          message: 'Registration Failed',
          description: 'Please try again later',
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = async (): Promise<void> => {
    const appLogPayload: Partial<AppLogPayloadI> = { url: 'google' };
    try {
      setIsGoogleSubmitting(true);
      await GoogleSignin.hasPlayServices();
      const currentGoogleUser = GoogleSignin.getCurrentUser();
      if (currentGoogleUser) await GoogleSignin.signOut();

      const response: SignInResponse = await GoogleSignin.signIn();
      appLogPayload['request'] = JSON.stringify(response);
      appLogPayload['response'] = '';

      if (isSuccessResponse(response) && response.data?.idToken) {
        const deviceToken = await getDeviceToken();
        const result = await dispatch(
          externalSignInAsync({
            externalToken: response.data.idToken,
            type: 'GOOGLE',
            ...(deviceToken ? { deviceToken } : {}),
          }),
        ).unwrap();

        if (result.token) {
          await setStorage('token', result.token);
          await setStorage('jwtToken', result.jwtToken);
          await setStorage('userProfileId', result.profileId);
          await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();

          try {
            await connectSocket(result.profileId);
          } catch (_socketError) {
            console.error('Failed to connect socket:', _socketError);
          }

          await navigateBasedOnUserState(result);
        }
      } else {
        throw new AppError('Google sign-up failed');
      }
    } catch (_error: any) {
      appLogPayload.response = JSON.stringify({ _error, msg: _error?.message });
      const errorMessage =
        typeof _error === 'string' ? _error : _error?.message || 'Google sign-up failed';
      showToast({
        type: 'error',
        message: capitalizeFirstLetter(errorMessage),
        description: 'Please try again later',
      });
    } finally {
      setIsGoogleSubmitting(false);
      try {
        await createAppLogAPI(appLogPayload as AppLogPayloadI);
      } catch (_error) {
        //
      }
    }
  };

  const handleAppleSignUp = async (): Promise<void> => {
    try {
      setIsAppleSubmitting(true);

      if (!appleAuth.isSupported) {
        throw new AppError('Apple Sign-In is not supported on this device');
      }

      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });

      if (!appleAuthRequestResponse?.user || !appleAuthRequestResponse?.identityToken) {
        throw new AppError('Apple sign-up was cancelled or incomplete');
      }

      if (appleAuthRequestResponse.realUserStatus !== appleAuth.UserStatus.LIKELY_REAL) {
        console.warn('User status indicates potential bot or suspicious activity');
      }

      const hasValidAuth =
        appleAuthRequestResponse.identityToken &&
        appleAuthRequestResponse.user &&
        appleAuthRequestResponse.realUserStatus === 1;

      if (hasValidAuth) {
        const deviceToken = await getDeviceToken();
        let userEmail = appleAuthRequestResponse.email;

        if (!userEmail && appleAuthRequestResponse.identityToken) {
          try {
            const decoded = jwtDecode<{ email?: string }>(appleAuthRequestResponse.identityToken);
            userEmail = decoded.email ?? null;
          } catch (jwtError) {
            console.error('JWT decode failed:', jwtError);
          }
        }

        const result = await dispatch(
          externalSignInAsync({
            externalToken: appleAuthRequestResponse.identityToken,
            type: 'APPLE',
            ...(userEmail && userEmail.trim() !== '' ? { email: userEmail } : {}),
            ...(deviceToken ? { deviceToken } : {}),
          }),
        ).unwrap();

        if (result.token) {
          await setStorage('token', result.token);
          await setStorage('jwtToken', result.jwtToken);
          await setStorage('userProfileId', result.profileId);
          await dispatch(fetchAndSaveUserProfile({ id: result.profileId })).unwrap();

          try {
            await connectSocket(result.profileId);
          } catch (_socketError) {
            console.error('Failed to connect socket:', _socketError);
          }

          await navigateBasedOnUserState(result);
        } else {
          throw new AppError('Invalid response from server');
        }
      }
    } catch (_error: unknown) {
      const errorMessage =
        typeof _error === 'string' ? _error : (_error as Error)?.message || 'Apple sign-up failed';
      const displayMessage = errorMessage.includes('com.apple')
        ? 'Apple signup failed'
        : errorMessage;

      showToast({
        type: 'error',
        message: displayMessage,
        description: 'Please try again later',
      });
    } finally {
      setIsAppleSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    showPrivacyModal,
    setShowPrivacyModal,
    showTermsModal,
    setShowTermsModal,
    handleGoogleSignUp,
    handleAppleSignUp,
    isGoogleSubmitting,
    isAppleSubmitting,
  };
};

export default useCreateAccount;
