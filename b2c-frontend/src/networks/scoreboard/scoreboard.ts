/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ScoreboardAPIResponseI } from '@/src/screens/Scoreboard/types';
import { apiCall } from '@/src/services/api';

export const fetchScoreboardAPI = async (): Promise<ScoreboardAPIResponseI[]> => {
  const result = await apiCall<unknown, ScoreboardAPIResponseI[]>(
    '/backend/api/v1/reward/scoreboard',
    'GET',
    {
      isAuth: true,
    },
  );
  return result;
};
