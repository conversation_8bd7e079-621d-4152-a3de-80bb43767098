import { useState, useEffect } from 'react';
import { View, Pressable, Text } from 'react-native';
import Information from '@/src/assets/svgs/Information';
import { AnonymousTimerPropsI } from './types';

const AnonymousTimer = ({ cooldownEnd }: AnonymousTimerPropsI) => {
  const [remaining, setRemaining] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipTimeout, setTooltipTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!cooldownEnd) {
      setRemaining(null);
      return;
    }

    const updateTimer = () => {
      const now = new Date();
      const endTime = new Date(cooldownEnd);
      const diff = endTime.getTime() - now.getTime();

      if (diff <= 0) {
        setRemaining(null);
        return;
      }
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      setRemaining(`${minutes}m`);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000);

    return () => {
      clearInterval(interval);
      if (tooltipTimeout) clearTimeout(tooltipTimeout);
    };
  }, [cooldownEnd]);

  const handlePress = () => {
    if (showTooltip) {
      setShowTooltip(false);
      if (tooltipTimeout) {
        clearTimeout(tooltipTimeout);
        setTooltipTimeout(null);
      }
      return;
    }

    setShowTooltip(true);

    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }

    setTooltipTimeout(
      setTimeout(() => {
        setShowTooltip(false);
        setTooltipTimeout(null);
      }, 3000),
    );
  };

  if (!remaining) return null;

  return (
    <View>
      <View className="bg-white pr-2 flex-row items-center rounded-lg">
        <Pressable onPress={handlePress} className="p-1">
          <Information width={2} height={2} />
        </Pressable>
        <Text className="text-xs text-black ml-1">{remaining}</Text>
      </View>
      {showTooltip && (
        <View className="absolute -left-28 top-6 bg-gray-400 rounded-lg p-2 z-50 w-48">
          <Text className="text-xs text-white">
            Once you go anonymous, you cannot change back for 1 hour
          </Text>
          <View className="absolute -top-1 right-16 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-400" />
        </View>
      )}
    </View>
  );
};

export default AnonymousTimer;
