import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI) => {
    const profileId = request.profileId;

    if (!profileId) {
      throw new AppError('AUTH019');
    }

    const result = await prismaPG.$queryRaw(
      Prisma.sql`
        SELECT * FROM "score"."RewardProfile"
        WHERE "profileId" = ${profileId}
      `
    );

    return result;
  },
};
