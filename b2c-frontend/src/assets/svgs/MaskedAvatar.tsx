import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Rect, Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const MaskedAvatar: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 3.1,
  color = '#404040',
  ...props
}) => {
  return (
    <Svg width={40} height={40} viewBox="0 0 40 40" fill="none" {...props}>
      <Rect
        x={1}
        y={1}
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        rx={19}
        fill="#D4D4D4"
      />
      <Rect x={1} y={1} width={38} height={38} rx={19} stroke="#000" strokeWidth={2} />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26 16a6 6 0 11-12 0 6 6 0 0112 0zm-2 0a4 4 0 11-8 0 4 4 0 018 0z"
        fill="#000"
      />
      <Path
        d="M20 25c-6.474 0-11.99 3.828-14.092 9.192.512.508 1.051.99 1.616 1.44C9.088 30.708 13.997 27 20 27c6.003 0 10.912 3.708 12.477 8.632.564-.45 1.103-.932 1.615-1.44C31.991 28.828 26.475 25 20 25z"
        fill="#000"
      />
    </Svg>
  );
};

export default MaskedAvatar;
