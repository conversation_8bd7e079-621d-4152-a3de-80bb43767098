import { useEffect, useRef, useCallback } from 'react';
import {
  ActivityIndicator,
  Pressable,
  Text,
  View,
  Animated,
  Easing,
  RefreshControl,
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Announcement } from '@/src/components/Announcement';
import { navigate } from '@/src/utilities/navigation';
import AddItem from '@/src/assets/svgs/AddItem';
import { useAnnouncements } from './useHook';

export const Announcements = () => {
  const {
    selfProfileId,
    announcements,
    loading,
    isLoadingMore,
    fetchAnnoucements,
    loadMoreAnnouncements,
    deleteAnnouncement,
    refetch,
  } = useAnnouncements();

  const insets = useSafeAreaInsets();
  const bottomNavHeight = 60;
  const fabBottom = insets.bottom + bottomNavHeight;

  const scaleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 400,
      easing: Easing.out(Easing.exp),
      useNativeDriver: true,
    }).start();
  }, []);

  const onRefresh = useCallback(() => {
    fetchAnnoucements(false);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  const renderEmptyComponent = () => (
    <View className="flex-1 justify-center items-center mt-20">
      <Text className="text-base text-gray-500">No events yet in your location</Text>
    </View>
  );

  return (
    <View className="flex-1 px-4">
      {loading && announcements.length === 0 ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={announcements}
          keyExtractor={(item) => item.announcementId}
          renderItem={({ item }) => (
            <Announcement
              announcement={item}
              user={item.profile}
              selfProfileId={selfProfileId}
              onDelete={deleteAnnouncement}
            />
          )}
          ListHeaderComponent={
            <View className="flex-row justify-between items-center py-2">
              <Text className="text-lg font-medium">{`All (${announcements.length})`}</Text>
            </View>
          }
          ListEmptyComponent={renderEmptyComponent}
          onEndReached={loadMoreAnnouncements}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
          }
          contentContainerStyle={{
            paddingBottom: fabBottom + 20,
          }}
        />
      )}
      <Animated.View
        style={{
          position: 'absolute',
          bottom: fabBottom,
          right: insets.right + 10,
          transform: [{ scale: scaleAnim }],
        }}
      >
        <Pressable
          style={{
            width: 56,
            height: 56,
            borderRadius: 28,
            backgroundColor: '#DDEFC8',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={() => navigate('EditAnnouncement', { refetch })}
        >
          <AddItem width={4} height={4} stroke="#DDEFC8" />
        </Pressable>
      </Animated.View>
    </View>
  );
};
