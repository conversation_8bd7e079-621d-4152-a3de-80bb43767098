import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import ScoreboardItem from './components/ScoreboardItem';

const ScoreboardScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleBack = () => {
    navigation.goBack();
  };

  const scoreboardData = [
    { category: 'Troubleshooting', score: 24, percentage: 80 },
    { category: 'QnA', score: 32, percentage: 80 },
  ];

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 pt-2 pb-4">
          <BackButton
            onBack={handleBack}
            label="My scoreboard"
            labelClassname="text-xl font-semibold text-gray-900"
          />
        </View>

        <View className="px-4">
          <View className="flex-row items-center justify-between mb-4 px-4 bg-[#F6F4F4] p-4 py-7">
            <Text className="text-base font-medium text-[#7E7D7D]">Category</Text>
            <Text className="text-base font-medium text-[#7E7D7D] text-center">Score</Text>
            <Text className="text-base font-medium text-[#7E7D7D]  text-center">Leaderboard Index</Text>
          </View>

          {/* Scoreboard Items */}
          {scoreboardData.map((item, index) => (
            <ScoreboardItem
              key={index}
              category={item.category}
              score={item.score}
              percentage={item.percentage}
            />
          ))}
        </View>
      </View>
    </SafeArea>
  );
};

export default ScoreboardScreen;