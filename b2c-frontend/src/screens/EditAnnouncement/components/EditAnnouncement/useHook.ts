import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectLocationData } from '@/src/redux/selectors/announcement';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearLocationData } from '@/src/redux/slices/announcement/announcementSlice';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { formatLatitude, formatLongitude } from '@/src/utilities/location/coordinates';
import { showToast } from '@/src/utilities/toast';
import { NearbyStackParamListI } from '@/src/navigation/types';
import { addAnnouncementAPI } from '@/src/networks/nearby/announcement';
import { addAnnouncementBodyI } from '@/src/networks/nearby/types';
import {
  AnnouncementDetailsFormDataI,
  CityCountryPostCodeDetails,
  LocationData,
  portCoordinatesSearchResultI,
} from './types';

export const useEditAnnouncement = (announcementId: string, refetch?: () => void) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();

  const locationData = useSelector(selectLocationData);
  const portSelection = useSelector(
    selectSelectionByKey('port-coordinates'),
  ) as unknown as portCoordinatesSearchResultI;

  const dispatch = useDispatch<AppDispatch>();

  const methods = useForm<AnnouncementDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      title: '',
      message: '',
      startDate: '',
      endDate: '',
      startTime: '',
      endTime: '',
    },
  });

  const hasChanges = methods.formState.isDirty;

  const onSubmit = async (data: AnnouncementDetailsFormDataI) => {
    try {
      setIsSubmitting(true);
      if (announcementId) {
        // await editEducationAPI(educationId, payload);
      } else {
        const payload = transformDataForCreate(data, portSelection, locationData);
        await addAnnouncementAPI(payload);
      }
      showToast({
        message: 'Success',
        description: announcementId
          ? 'Announcement updated successfully'
          : 'Announcement added successfully',
        type: 'success',
      });
      dispatch(clearLocationData());
      dispatch(clearSelection('city'));
      if (refetch) refetch();
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Announcement',
            description: 'Unable to save Announcement',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const clearFields = () => {
    dispatch(clearSelection('port'));
    dispatch(clearLocationData());
  };

  return {
    methods,
    isSubmitting,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
    onSubmit,
    navigation,
    clearFields,
  };
};

const transformDataForCreate = (
  data: AnnouncementDetailsFormDataI,
  port: portCoordinatesSearchResultI,
  locationData: LocationData,
): addAnnouncementBodyI => {
  const { message, startTime, endTime, ...rest } = data;

  const extractTime = (time: string | null | undefined): string | null => {
    const match = time?.match(/^(\d{2}:\d{2})/);
    return match ? match[1] : null;
  };

  const formattedStartTime = extractTime(startTime);
  const formattedEndTime = extractTime(endTime);

  if (!formattedStartTime || !formattedEndTime) {
    throw new Error('Invalid start or end time format.');
  }

  const result: addAnnouncementBodyI = {
    ...rest,
    description: message,
    startTime: formattedStartTime,
    endTime: formattedEndTime,
  };

  if (port) {
    result.city = {
      id: port.city.id,
      dataType: port.city.dataType,
    };
    result.port = {
      unLocode: port.unLocode,
      name: port.name,
      dataType: port.dataType,
      latitude: formatLatitude(Number(port.latitude)),
      longitude: formatLongitude(Number(port.longitude)),
    };
  }

  if (locationData.id) {
    result.longitude = locationData.center[0];
    result.latitude = locationData.center[1];
    result.addressMapBox = {
      id: locationData.id,
      text: locationData.text,
    };

    const cityCountryPostCodeDetails = locationData.context.reduce<CityCountryPostCodeDetails>(
      (acc, item) => {
        if (item.id && item.text) {
          if (/^postcode\./.test(item.id)) {
            acc.postcode ??= { id: item.id, text: item.text };
          }
          if (/^place\./.test(item.id)) {
            acc.place ??= { id: item.id, text: item.text };
          }
          if (/^country\./.test(item.id)) {
            acc.country ??= { id: item.id, short_code: item.short_code };
          }
        }
        return acc;
      },
      {},
    );

    if (cityCountryPostCodeDetails.place) {
      result.cityMapBox = {
        id: cityCountryPostCodeDetails.place.id,
        name: cityCountryPostCodeDetails.place.text,
      };
    }

    if (cityCountryPostCodeDetails.country?.short_code) {
      result.countryIso2 = cityCountryPostCodeDetails.country.short_code.toUpperCase();
    }

    if (cityCountryPostCodeDetails.postcode) {
      result.postCodeMapBox = {
        id: cityCountryPostCodeDetails.postcode.id,
        text: cityCountryPostCodeDetails.postcode.text,
      };
    }
  }

  return result;
};
