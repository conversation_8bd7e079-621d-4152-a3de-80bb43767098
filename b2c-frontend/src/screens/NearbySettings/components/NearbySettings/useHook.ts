import { useEffect, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import { useForm, useFieldArray, useWatch } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import {
  clearOtherLocation,
  setNearbyRadius,
  setSelfLocation,
} from '@/src/redux/slices/announcement/announcementSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import type { MapboxSuggestion, NearbySettingsFormFiltersI } from './types';

export const useNearbySettings = () => {
  const nearbyFilters = useSelector(selectNearbyFilters);
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  const [loading, setLoading] = useState(false);
  const [isOtherLocationAdded,setIsOtherLocationAdded] = useState(false);
  console.log("nearbyFil = ",nearbyFilters)

  const methods = useForm<NearbySettingsFormFiltersI>({
    mode: 'onChange',
    defaultValues: {
      radius: nearbyFilters.radius || 0,
    },
  });

  const isSelfLocationValid =
    nearbyFilters.selfLocation?.coords?.latitude !== undefined &&
    nearbyFilters.selfLocation?.coords?.longitude !== undefined &&
    !(
      nearbyFilters.selfLocation.coords.latitude === 0 &&
      nearbyFilters.selfLocation.coords.longitude === 0
    );

  const isOtherLocationValid =
    nearbyFilters.otherLocation?.coords?.latitude !== undefined &&
    nearbyFilters.otherLocation?.coords?.longitude !== undefined &&
    !(
      nearbyFilters.otherLocation.coords.latitude === 0 &&
      nearbyFilters.otherLocation.coords.longitude === 0
    );

  const selfLocation = nearbyFilters.selfLocation
  const otherLocation = nearbyFilters.otherLocation

  useEffect(() => {
    const getCurrentLocation = () => {
      if (isSelfLocationValid) return;

      Geolocation.getCurrentPosition(
        async (position) => {
          const { longitude, latitude } = position.coords;
          try {
            const response = await fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
            );
            const data = await response.json();
            const addressItem = data.features?.find((item: MapboxSuggestion) =>
              item.id.startsWith('address.'),
            );
            const placeName =
              addressItem?.place_name || data.features?.[0]?.place_name || 'Current Location';
            dispatch(setSelfLocation({ coords: { latitude, longitude }, name: placeName }));
          } catch (err) {
            showToast({ type: 'error', message: 'Failed to get location details' });
          }
        },
        (err) => {
          showToast({ type: 'error', message: err.message });
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          } else {
            showToast({ type: 'error', message: 'Location permission denied' });
          }
        } catch (err) {
          showToast({ type: 'error', message: 'Failed to request location permission' });
        }
      } else if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');
        if (status === 'granted') {
          getCurrentLocation();
        } else {
          showToast({ type: 'error', message: 'Location permission denied' });
        }
      } else {
        getCurrentLocation();
      }
    };

    requestLocationPermission();
  }, [dispatch]);

  const handleAddOtherLocation = () => {
    setIsOtherLocationAdded(true)
  };

  const handleDeleteOtherLocation = () => {
      dispatch(clearOtherLocation());
      setIsOtherLocationAdded(false);
  };



  const onSubmit = (data: NearbySettingsFormFiltersI) => {
    setLoading(true);
    setTimeout(() => {
      dispatch(setNearbyRadius(data.radius));
      setLoading(false);
      navigation.goBack();
    }, 200);
  };

  return {
    methods,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    onSubmit,
    isSelfLocationValid,
    isOtherLocationValid,
    selfLocation,
    otherLocation
  };
};
