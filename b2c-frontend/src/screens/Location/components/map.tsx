import { useEffect, useRef, useState } from 'react';
import {
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
} from 'react-native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import Mapbox from '@rnmapbox/maps';
import TextInput from '@/src/components/TextInput';

const mapboxToken = Config.MAPBOX_ACCESS_TOKEN;
Mapbox.setAccessToken(mapboxToken);

const Map = ({ onLocationSelect }: { onLocationSelect: (location: any) => void }) => {
  const [city, setCity] = useState('');
  const [error, setError] = useState('');
  const [location, setLocation] = useState<number[]>([-122.4324, 37.7832]);
  const [locationResults, setLocationResults] = useState<any[]>([]);
  const [isSelecting, setIsSelecting] = useState(false);
  const cameraRef = useRef<Mapbox.Camera>(null);

  useEffect(() => {
    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          } else {
            setError('Location permission denied');
          }
        } catch (err) {
          setError('Error requesting location permission');
        }
      } else if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');
        if (status === 'granted') {
          getCurrentLocation();
        } else {
          setError('Location permission denied on iOS');
        }
      } else {
        getCurrentLocation();
      }
    };

    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        (position) => {
          const newLocation: [number, number] = [
            position.coords.longitude,
            position.coords.latitude,
          ];
          setLocation(newLocation);
        },
        (err) => {
          setError(err.message);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    requestLocationPermission();
  }, []);

  useEffect(() => {
    const fetchLocation = async () => {
      if (isSelecting) {
        setIsSelecting(false);
        return;
      }
      if (city.trim().length > 0) {
        try {
          const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
              city,
            )}.json?access_token=${mapboxToken}`,
          );
          const data = await response.json();
          if (data.features && data.features.length > 0) {
            setLocationResults(data.features);
            setError('');
          } else {
            setError('Location not found');
            setLocationResults([]);
          }
        } catch (err) {
          setError('Error fetching location data');
          setLocationResults([]);
          console.error(err);
        }
      } else {
        setLocationResults([]);
        setError('');
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchLocation();
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [city]);

  const handleLocationSelect = (selectedLocation: any) => {
    const [longitude, latitude] = selectedLocation.center;
    setLocation([longitude, latitude]);
    setIsSelecting(true);
    setCity(selectedLocation.place_name);
    setLocationResults([]);
  };

  const handleRegionChange = async (region: { geometry: { coordinates: any[] } }) => {
    const centerCoord = [region.geometry.coordinates[0], region.geometry.coordinates[1]];
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${centerCoord[0]},${centerCoord[1]}.json?access_token=${mapboxToken}`,
    );
    const data = await response.json();
    data.features.map((item: any) => {
      if (/^address\./.test(item.id)) {
        onLocationSelect(item);
      }
    });
  };

  return (
    <View className="flex-1">
      <View className="pt-3 px-2">
        <TextInput value={city} onChangeText={setCity} placeholder="Search for a city..." />
        {error ? <Text className="text-red-500 mt-2">{error}</Text> : null}
      </View>
      <View className="pt-4 relative flex-1">
        <View className="flex-1 w-full">
          {location && (
            <>
              <Mapbox.MapView style={styles.map} onRegionDidChange={handleRegionChange}>
                <Mapbox.Camera
                  ref={cameraRef}
                  centerCoordinate={location}
                  zoomLevel={14}
                  animationMode="flyTo"
                />
              </Mapbox.MapView>
              <View className="absolute top-1/2 left-1/2 -ml-[15px] -mt-[30px] z-10 pointer-events-none">
                <View className="absolute bottom-0 left-1/2 -ml-[7px] w-[14px] h-[14px] bg-blue-500 rounded-full border-2 border-white" />
                <View className="w-[30px] h-[30px] bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                  <View className="w-[10px] h-[10px] bg-white rounded-full" />
                </View>
                <View className="absolute top-[28px] left-1/2 -ml-[1px] w-[2px] h-[10px] bg-blue-500" />
              </View>
            </>
          )}
        </View>
        {locationResults.length > 0 && (
          <View className="absolute top-5 left-1 right-1 bg-white rounded-lg shadow-md max-h-48 z-10">
            <FlatList
              data={locationResults}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  className="px-4 py-3 border-b border-gray-100"
                  onPress={() => handleLocationSelect(item)}
                >
                  <Text>{item.place_name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  map: {
    flex: 1,
  },
  centerMarkerContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 1,
  },
  centerMarker: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 122, 255, 0.5)',
    borderWidth: 2,
    borderColor: 'white',
  },
  point: {
    position: 'absolute',
  },
});

export default Map;
