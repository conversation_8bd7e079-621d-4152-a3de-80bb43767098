import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import RewardModule from '@modules/reward';

import { FastifyInstance, FastifyReply } from 'fastify';

const scoreboardRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/reward/scoreboard', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const result = await RewardModule.ScoreboardModule.fetch(request);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default scoreboardRoutes;
