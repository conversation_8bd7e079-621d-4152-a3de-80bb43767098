export type RewardProfileI = {
  profileId: string;
  contributionScore: number;
  qnaAnswerScore: number;
  troubleshootAnswerScore: number;
  totalScore: number;
  createdAt: Date;
  updatedAt: Date;
};

export type ScoreboardWithPerformanceI = RewardProfileI & {
  qnaPerformanceIndex: number;
  troubleshootPerformanceIndex: number;
  contributionPerformanceIndex: number;
};

export type ScoreboardFetchForClientResultI = ScoreboardWithPerformanceI;
