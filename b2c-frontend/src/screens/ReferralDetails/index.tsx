import { useState, useRef } from 'react';
import { View, Text, TextInput, Pressable, ScrollView, FlatList } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import Clipboard from '@react-native-clipboard/clipboard';
import BackButton from '@/src/components/BackButton';
import ReferralTermsModal from '@/src/components/ReferralTerms';
import SafeArea from '@/src/components/SafeArea';
import ShimmerBox from '@/src/components/Shimmer';
import type { RootDrawerParamListI } from '@/src/navigation/types';
import type { ReferredPerson } from '@/src/networks/referral/types';
import { useReferralCode } from './useHook';

const ReferralDetailsScreen = () => {
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const [modalVisible, setModalVisible] = useState(false);
  const [copied, setCopied] = useState(false);
  const textInputRef = useRef(null);
  const { loading, referralCode, referred, loadMore, hasMore } = useReferralCode();

  const handleCopy = () => {
    Clipboard.setString(referralCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const renderReferral = ({ item }: { item: ReferredPerson }) => (
    <View className="bg-[#F5F5F5] p-4 rounded-xl mb-4 flex-row items-center justify-between">
      <View>
        <Text className="font-semibold text-[#1A2B40] text-base">{item.name}</Text>
        {item.designation?.name && (
          <Text className="text-sm text-gray-600">{item.designation.name}</Text>
        )}
        {item.entity?.name && <Text className="text-sm text-gray-600">{item.entity.name}</Text>}
      </View>
      <Text className="text-green-600 font-bold text-base">+10 pts</Text>
    </View>
  );

  const renderFlatListShimmer = () => (
    <View>
      {[...Array(3)].map((_, i) => (
        <ShimmerBox key={i} width="100%" height={60} className="mb-4" />
      ))}
    </View>
  );

  const renderNotFound = () => (
    <View className="flex-1 items-center justify-center py-10 px-4">
      <Text className="text-lg font-semibold text-gray-600 mb-2">No Referrals Yet!</Text>
      <Text className="text-center text-gray-500">
        Start sharing your referral code to earn points and unlock special gifts.
      </Text>
    </View>
  );

  return (
    <SafeArea>
      <BackButton onBack={() => navigation.goBack()} />
      <ScrollView className="flex-1 bg-white" keyboardShouldPersistTaps="handled">
        <View className="px-6 py-5">
          <Text className="text-2xl font-bold text-center mb-6 text-[#1A2B40]">
            Earn points by referring friends
          </Text>
          <Text className="text-sm font-semibold text-gray-700 mb-1">Your referral code is</Text>
          <View className="flex-row items-center bg-gray-100 rounded-lg p-2 mb-6">
            <TextInput
              ref={textInputRef}
              value={referralCode}
              editable={false}
              className="flex-1 text-lg text-gray-800 px-2 py-1 font-mono"
            />
            <Pressable onPress={handleCopy} className="bg-gray-300 px-3 py-2 rounded-md ml-2">
              <Text className="text-gray-800 font-medium">{copied ? 'Copied!' : 'Copy'}</Text>
            </Pressable>
          </View>
          <Pressable
            onPress={() => setModalVisible(true)}
            className="bg-[#FFD700] px-4 py-6 rounded-xl mb-6 flex-row items-center space-x-3"
          >
            <View className="flex-1">
              <Text className="text-base font-bold text-[#1A2B40]">
                Earn 100+ points and unlock a special gift from NAVICATER
              </Text>
              <Text className="text-xs text-gray-700 mt-3">
                *Limited time offer - Tap for details & conditions
              </Text>
            </View>
          </Pressable>
          <Text className="text-base font-semibold mb-4 text-[#1A2B40]">Points earned</Text>
          {loading && referred.length === 0 ? (
            renderFlatListShimmer()
          ) : referred.length > 0 ? (
            <FlatList
              data={referred}
              renderItem={renderReferral}
              keyExtractor={(item, index) => `${item.id}-${index}`}
              onEndReached={() => hasMore && loadMore()}
              onEndReachedThreshold={0.5}
              showsVerticalScrollIndicator={false}
              ListFooterComponent={
                hasMore ? <ShimmerBox width="100%" height={60} className="rounded-xl my-4" /> : null
              }
            />
          ) : (
            renderNotFound()
          )}
        </View>
      </ScrollView>
      <ReferralTermsModal isVisible={modalVisible} onClose={() => setModalVisible(false)} />
    </SafeArea>
  );
};

export default ReferralDetailsScreen;
