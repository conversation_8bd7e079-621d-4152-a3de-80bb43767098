 import { View } from 'react-native';
 import ShimmerBox from '@/src/components/Shimmer';
 

const ForumPostSkeleton = () => (
  <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg mx-2">
    <View className="px-2 flex-row gap-2 items-center mb-2">
      <View className="flex-row gap-2">
        <ShimmerBox width={80} height={28} borderRadius={14} />
        <ShimmerBox width={60} height={28} borderRadius={14} />
        <ShimmerBox width={70} height={28} borderRadius={14} />
      </View>
    </View>

    <View className="px-3 py-2">
      <ShimmerBox width={'90%'} height={22} borderRadius={4} className="mb-1" />
      <ShimmerBox width={'70%'} height={22} borderRadius={4} />
    </View>

    <View className="py-1 px-3 mb-2">
      <ShimmerBox width={'100%'} height={18} borderRadius={4} className="mb-1" />
      <ShimmerBox width={'85%'} height={18} borderRadius={4} />
    </View>

    <View className="px-2 mb-2">
      <View className="flex-row gap-2">
        <ShimmerBox width={40} height={40} borderRadius={8} />
        <ShimmerBox width={40} height={40} borderRadius={8} />
        <ShimmerBox width={40} height={40} borderRadius={8} />
      </View>
    </View>

    <View className="flex-row justify-between items-center px-4">
      <View className="flex-row items-center gap-5">
        <View className="flex-row items-center gap-2">
          <ShimmerBox width={24} height={24} borderRadius={4} />
          <ShimmerBox width={20} height={16} borderRadius={4} />
        </View>
        <View className="flex-row items-center gap-2">
          <ShimmerBox width={24} height={24} borderRadius={4} />
          <ShimmerBox width={20} height={16} borderRadius={4} />
        </View>
        <View className="flex-row items-center gap-2">
          <ShimmerBox width={24} height={24} borderRadius={4} />
          <ShimmerBox width={20} height={16} borderRadius={4} />
        </View>
      </View>
      <ShimmerBox width={24} height={24} borderRadius={4} />
    </View>
  </View>
);

export default ForumPostSkeleton;
