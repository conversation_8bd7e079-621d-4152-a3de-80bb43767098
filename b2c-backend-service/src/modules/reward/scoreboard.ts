import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI } from '@interfaces/reward/scoreboard';
import type { FastifyRequestI } from '@interfaces/common/declaration';

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI): Promise<ScoreboardFetchForClientResultI[]> => {
    const profileId = request.profileId;
    
    if (!profileId) {
      throw new AppError('AUTH019');
    }

    // Get user's reward profile
    const userRewardProfile = await prismaPG.rewardProfile.findUnique({
      where: { profileId },
      select: {
        contributionScore: true,
        qnaAnswerScore: true,
        troubleshootAnswerScore: true,
        totalScore: true,
      },
    });

    // If user has no reward profile, return empty scores
    if (!userRewardProfile) {
      return [
        {
          category: 'Mentor',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Guide',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Troubleshooter',
          score: 0,
          leaderboardIndex: 0,
        },
        {
          category: 'Contributor',
          score: 0,
          leaderboardIndex: 0,
        },
      ];
    }

    // Get highest scores for each category to calculate leaderboard index
    const maxScores = await prismaPG.rewardProfile.aggregate({
      _max: {
        contributionScore: true,
        qnaAnswerScore: true,
        troubleshootAnswerScore: true,
        totalScore: true,
      },
    });

    // Calculate leaderboard index: (user score / highest score) * 100
    const calculateLeaderboardIndex = (userScore: number, maxScore: number | null): number => {
      if (!maxScore || maxScore === 0) return 0;
      return Math.round((userScore / maxScore) * 100);
    };

    // Map the scores to the frontend categories
    const scoreboardData: ScoreboardFetchForClientResultI[] = [
      {
        category: 'Mentor',
        score: userRewardProfile.totalScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.totalScore,
          maxScores._max.totalScore
        ),
      },
      {
        category: 'Guide',
        score: userRewardProfile.qnaAnswerScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.qnaAnswerScore,
          maxScores._max.qnaAnswerScore
        ),
      },
      {
        category: 'Troubleshooter',
        score: userRewardProfile.troubleshootAnswerScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.troubleshootAnswerScore,
          maxScores._max.troubleshootAnswerScore
        ),
      },
      {
        category: 'Contributor',
        score: userRewardProfile.contributionScore,
        leaderboardIndex: calculateLeaderboardIndex(
          userRewardProfile.contributionScore,
          maxScores._max.contributionScore
        ),
      },
    ];

    return scoreboardData;
  },
};
